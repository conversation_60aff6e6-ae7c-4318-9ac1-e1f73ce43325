{"openapi": "3.0.0", "paths": {"/football/fixtures/upcoming-and-live": {"get": {"description": "\n    Retrieve upcoming and live football fixtures with smart filtering.\n\n    **Features:**\n    - Real-time fixture status updates\n    - Smart time-based filtering (2.5-hour window)\n    - Optimized performance (96% API call reduction)\n    - Pagination support\n    - No authentication required\n\n    **Status Logic:**\n    - UPCOMING: Fixtures starting in 5-10 minutes\n    - LIVE: Fixtures currently in progress\n    - Real-time updates every 10 seconds\n    ", "operationId": "FixtureController_getUpcomingAndLiveFixtures", "parameters": [{"name": "leagueId", "required": false, "in": "query", "description": "Filter by specific league ID", "schema": {"example": 39, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of fixtures per page (default: 10, max: 100)", "schema": {"example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination (default: 1)", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Upcoming and live fixtures retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 868847, "date": "2025-05-24T15:00:00.000Z", "status": "UPCOMING", "homeTeam": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png"}, "awayTeam": {"id": 34, "name": "Newcastle", "logo": "https://media.api-sports.io/football/teams/34.png"}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png"}, "venue": {"name": "Old Trafford", "city": "Manchester"}, "score": {"home": null, "away": null}}], "meta": {"totalItems": 25, "totalPages": 3, "currentPage": 1, "limit": 10}, "status": 200}}}}}, "summary": "Get Upcoming and Live Fixtures", "tags": ["Football - Fixtures"]}}, "/football/fixtures/sync/fixtures": {"get": {"description": "\n    Manually trigger synchronization of fixtures for active seasons.\n\n    **Features:**\n    - Syncs current and previous year fixtures\n    - Only processes active leagues\n    - Batch processing with error isolation\n    - Returns sync statistics\n    - Requires admin authentication\n\n    **Use Cases:**\n    - Initial data population\n    - Manual data refresh\n    - Recovery from sync failures\n    - Testing sync functionality\n    ", "operationId": "FixtureController_triggerSeasonFixturesSync", "parameters": [], "responses": {"200": {"description": "Season fixtures sync triggered successfully", "content": {"application/json": {"example": {"status": "Sync triggered", "fixturesUpserted": 1250, "message": "Successfully synced fixtures for 2024 and 2025 seasons"}}}}, "403": {"description": "Forbidden - Admin access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403}}}}, "500": {"description": "Sync failed", "content": {"application/json": {"example": {"status": "Error", "message": "Failed to sync season fixtures: API rate limit exceeded"}}}}}, "security": [{"bearer": []}], "summary": "Trigger Season Fixtures Sync (Admin Only)", "tags": ["Football - Fixtures", "Data Synchronization"]}}, "/football/fixtures/sync/daily": {"get": {"description": "\n    Manually trigger daily synchronization of all active league fixtures.\n\n    **Features:**\n    - Syncs all active leagues for current day\n    - Full data refresh with smart upsert protection\n    - Batch processing with error isolation\n    - Returns detailed sync statistics\n    - Requires admin authentication\n\n    **Use Cases:**\n    - Manual daily data refresh\n    - Recovery from failed cronjobs\n    - Testing sync functionality\n    - Initial data population\n\n    **Performance:**\n    - Smart time-based filtering\n    - Batch processing (50 fixtures per batch)\n    - Error isolation per league\n    - Cache invalidation after sync\n    ", "operationId": "FixtureController_triggerDailySync", "parameters": [], "responses": {"200": {"description": "Daily sync triggered successfully", "content": {"application/json": {"example": {"status": "Success", "message": "Daily sync completed successfully", "success": true, "stats": {"leaguesProcessed": 18, "fixturesUpserted": 245, "errors": 0, "duration": "2.3s"}}}}}, "403": {"description": "Forbidden - Admin access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403}}}}, "500": {"description": "Daily sync failed", "content": {"application/json": {"example": {"status": "Error", "message": "Failed to trigger daily sync: API rate limit exceeded", "success": false}}}}}, "security": [{"bearer": []}], "summary": "<PERSON>gger Daily Sync (Admin Only)", "tags": ["Football - Fixtures", "Data Synchronization"]}}, "/football/fixtures/sync/status": {"get": {"description": "\n    Retrieve current synchronization status and statistics.\n\n    **Features:**\n    - Last sync timestamp (UTC)\n    - Today's fixtures count\n    - Error tracking and reporting\n    - Real-time status monitoring\n    - Requires editor+ authentication\n\n    **Use Cases:**\n    - Monitor sync health\n    - Debug sync issues\n    - Performance monitoring\n    - System status dashboard\n\n    **Response Data:**\n    - lastSync: ISO timestamp of last sync\n    - fixtures: Count of today's fixtures\n    - errors: Array of recent sync errors\n    ", "operationId": "FixtureController_getSyncStatus", "parameters": [], "responses": {"200": {"description": "Sync status retrieved successfully", "content": {"application/json": {"example": {"lastSync": "2025-05-24T10:48:24.216Z", "fixtures": 245, "errors": []}}}}, "403": {"description": "Forbidden - Editor+ access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403}}}}}, "security": [{"bearer": []}], "summary": "Get Sync Status (Editor+)", "tags": ["Football - Fixtures", "Data Synchronization"]}}, "/football/fixtures/schedules/{teamId}": {"get": {"description": "\n    Retrieve fixtures schedule for a specific team.\n\n    **Features:**\n    - Complete team fixture history\n    - Upcoming and past matches\n    - Pagination support\n    - Date range filtering\n    - Authentication required for API usage tracking\n\n    **Tier Access:**\n    - Free: 100 API calls/month\n    - Premium: 10,000 API calls/month\n    - Enterprise: Unlimited API calls\n\n    **Use Cases:**\n    - Team fixture calendar\n    - Match history analysis\n    - Upcoming games preview\n    - Season schedule overview\n    ", "operationId": "FixtureController_getTeamSchedule", "parameters": [{"name": "teamId", "required": true, "in": "path", "description": "Team external ID (positive integer)", "schema": {"example": 33, "type": "number"}}, {"name": "to", "required": false, "in": "query", "description": "End date filter (YYYY-MM-DD)", "schema": {"example": "2025-12-31", "type": "string"}}, {"name": "from", "required": false, "in": "query", "description": "Start date filter (YYYY-MM-DD)", "schema": {"example": "2025-01-01", "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of fixtures per page", "schema": {"example": 20, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Team schedule retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 868847, "date": "2025-05-24T15:00:00.000Z", "status": "FT", "homeTeam": {"id": 33, "name": "Manchester United"}, "awayTeam": {"id": 34, "name": "Newcastle"}, "score": {"home": 2, "away": 1}, "league": {"name": "Premier League"}}], "meta": {"totalItems": 38, "totalPages": 2, "currentPage": 1, "limit": 20}}}}}, "400": {"description": "Invalid team ID", "content": {"application/json": {"example": {"message": "Invalid teamId: must be a positive integer", "error": "Bad Request", "statusCode": 400}}}}}, "security": [{"bearer": []}], "summary": "Get Team Schedule", "tags": ["Football - Fixtures"]}}, "/football/fixtures/statistics/{externalId}": {"get": {"operationId": "FixtureController_getFixtureStatistics", "parameters": [{"name": "externalId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Football - Fixtures"]}}, "/football/fixtures": {"get": {"description": "\n    Retrieve fixtures with comprehensive filtering options.\n\n    **Access:** Public endpoint (no authentication required)\n\n    **Query Parameters:**\n    - page, limit: Pagination\n    - league, season: Filter by league/season\n    - team, venue: Filter by team/venue\n    - date: Filter by specific date\n    - status: Filter by status (NS, LIVE, FT, etc.)\n    - timezone: Timezone (default: UTC)\n    - from, to: Date range filtering\n\n    **Examples:**\n    - ?league=39&season=2024 (Premier League 2024)\n    - ?team=33&status=FT (Manchester United finished matches)\n    - ?from=2024-01-01&to=2024-12-31 (Year 2024)\n    - ?date=2024-05-24 (Specific date)\n    ", "operationId": "FixtureController_getFixtures", "parameters": [{"name": "to", "required": false, "in": "query", "description": "End date (YYYY-MM-DD)", "schema": {"example": "2024-12-31", "type": "string"}}, {"name": "from", "required": false, "in": "query", "description": "Start date (YYYY-MM-DD)", "schema": {"example": "2024-01-01", "type": "string"}}, {"name": "timezone", "required": false, "in": "query", "description": "Timezone", "schema": {"example": "UTC", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Status (NS,LIVE,FT)", "schema": {"example": "LIVE,FT", "type": "string"}}, {"name": "date", "required": false, "in": "query", "description": "Date (YYYY-MM-DD)", "schema": {"example": "2024-05-24", "type": "string"}}, {"name": "venue", "required": false, "in": "query", "description": "Venue ID", "schema": {"example": 556, "type": "number"}}, {"name": "team", "required": false, "in": "query", "description": "Team ID", "schema": {"example": 33, "type": "number"}}, {"name": "season", "required": false, "in": "query", "description": "Season year", "schema": {"example": 2024, "type": "number"}}, {"name": "league", "required": false, "in": "query", "description": "League ID (e.g., 39 for Premier League)", "schema": {"example": 39, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Fixtures retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 868847, "date": "2025-05-24T15:00:00.000Z", "status": "FT", "homeTeam": {"id": 33, "name": "Manchester United"}, "awayTeam": {"id": 34, "name": "Newcastle"}, "score": {"home": 2, "away": 1}, "league": {"name": "Premier League"}}], "meta": {"totalItems": 1250, "totalPages": 125, "currentPage": 1, "limit": 10}}}}}}, "summary": "Get Fixtures with Filters (Public)", "tags": ["Football - Fixtures"]}, "post": {"operationId": "FixtureController_createFixture", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFixtureDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Football - Fixtures"]}}, "/football/fixtures/{externalId}": {"get": {"description": "\n    Retrieve detailed information for a specific fixture by external ID.\n\n    **Access:** Public endpoint (no authentication required)\n\n    **Parameter:**\n    - externalId: Fixture external ID (positive integer)\n\n    **Examples:**\n    - /1274453 (Dreams vs Samartex)\n    - /868847 (Manchester United vs Liverpool)\n    - /1234567 (Any fixture external ID)\n    ", "operationId": "FixtureController_getFixtureById", "parameters": [{"name": "externalId", "required": true, "in": "path", "description": "Fixture external ID", "schema": {"example": 1274453, "type": "number"}}], "responses": {"200": {"description": "Fixture retrieved successfully", "content": {"application/json": {"example": {"data": {"id": 11, "externalId": 1274453, "leagueId": 570, "leagueName": "Premier League", "season": 2024, "homeTeamName": "Dreams", "awayTeamName": "Samartex", "date": "2024-09-07T15:00:00.000Z", "status": "FT", "goalsHome": 0, "goalsAway": 0}, "status": 200}}}}, "400": {"description": "Invalid external ID"}, "404": {"description": "Fixture not found"}}, "summary": "Get Fixture by ID (Public)", "tags": ["Football - Fixtures"]}, "patch": {"operationId": "FixtureController_updateFixture", "parameters": [{"name": "externalId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFixtureDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Football - Fixtures"]}}, "/football/leagues": {"get": {"description": "\n        Retrieve all football leagues with filtering and pagination.\n\n        **Features:**\n        - Complete league database\n        - Country-based filtering\n        - Active/inactive status filtering\n        - Pagination support\n        - No authentication required\n\n        **Popular Leagues:**\n        - Premier League (England) - ID: 39\n        - La Liga (Spain) - ID: 140\n        - Serie A (Italy) - ID: 135\n        - Bundesliga (Germany) - ID: 78\n        - Ligue 1 (France) - ID: 61\n        ", "operationId": "LeagueController_getLeagues", "parameters": [{"name": "active", "required": false, "in": "query", "description": "Filter by active status", "schema": {"example": true, "type": "boolean"}}, {"name": "country", "required": false, "in": "query", "description": "Filter by country name", "schema": {"example": "England", "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of leagues per page", "schema": {"example": 20, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Leagues retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb.svg", "season": 2025, "active": true, "type": "League"}], "meta": {"totalItems": 150, "totalPages": 8, "currentPage": 1, "limit": 20}}}}}}, "summary": "Get All Leagues", "tags": ["Football - Leagues"]}, "post": {"operationId": "LeagueController_createLeague", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLeagueDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Football - Leagues"]}}, "/football/leagues/{externalId}": {"get": {"operationId": "LeagueController_getLeagueById", "parameters": [{"name": "externalId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "season", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Football - Leagues"]}}, "/football/leagues/{id}": {"patch": {"operationId": "LeagueController_updateLeague", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLeagueDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Football - Leagues"]}}, "/football/teams": {"get": {"description": "\n        Retrieve teams with comprehensive filtering options.\n\n        **Query Parameters:**\n        - page, limit: Pagination\n        - league, season: Filter by league/season\n        - country: Filter by country name\n\n        **Examples:**\n        - ?league=39&season=2024 (Premier League 2024 teams)\n        - ?country=England (All English teams)\n        - ?league=140&season=2024 (La Liga 2024 teams)\n        ", "operationId": "TeamController_getTeams", "parameters": [{"name": "country", "required": false, "in": "query", "description": "Country name", "schema": {"example": "England", "type": "string"}}, {"name": "season", "required": false, "in": "query", "description": "Season year", "schema": {"example": 2024, "type": "number"}}, {"name": "league", "required": false, "in": "query", "description": "League ID", "schema": {"example": 39, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Teams retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 33, "name": "Manchester United", "code": "MUN", "country": "England", "founded": 1878, "logo": "https://media.api-sports.io/football/teams/33.png"}], "meta": {"totalItems": 20, "totalPages": 2, "currentPage": 1, "limit": 10}}}}}}, "security": [{"bearer": []}], "summary": "Get Teams with Filters", "tags": ["Football - Teams"]}}, "/football/teams/statistics": {"get": {"description": "\n        Retrieve detailed team statistics for a specific league and season.\n\n        **Required Parameters:**\n        - league: League ID (e.g., 39 for Premier League)\n        - season: Season year (e.g., 2024)\n        - team: Team ID (e.g., 33 for Manchester United)\n\n        **Examples:**\n        - ?league=39&season=2024&team=33 (Manchester United in Premier League 2024)\n        - ?league=140&season=2024&team=529 (Barcelona in La Liga 2024)\n        ", "operationId": "TeamController_getTeamStatistics", "parameters": [{"name": "team", "required": true, "in": "query", "description": "Team ID", "schema": {"example": 33, "type": "number"}}, {"name": "season", "required": true, "in": "query", "description": "Season year", "schema": {"example": 2024, "type": "number"}}, {"name": "league", "required": true, "in": "query", "description": "League ID", "schema": {"example": 39, "type": "number"}}], "responses": {"200": {"description": "Team statistics retrieved successfully", "content": {"application/json": {"example": {"data": {"teamId": 33, "leagueId": 39, "season": 2024, "fixtures": {"played": {"home": 19, "away": 19, "total": 38}, "wins": {"home": 12, "away": 8, "total": 20}, "draws": {"home": 4, "away": 6, "total": 10}, "loses": {"home": 3, "away": 5, "total": 8}}, "goals": {"for": {"total": {"home": 35, "away": 22, "total": 57}}, "against": {"total": {"home": 18, "away": 25, "total": 43}}}}, "status": 200}}}}}, "security": [{"bearer": []}], "summary": "Get Team Statistics", "tags": ["Football - Teams"]}}, "/football/teams/{externalId}": {"get": {"description": "\n        Retrieve detailed information for a specific team by external ID.\n\n        **Parameter:**\n        - externalId: Team external ID (positive integer)\n\n        **Examples:**\n        - /33 (Manchester United)\n        - /529 (Barcelona)\n        - /50 (Manchester City)\n        ", "operationId": "TeamController_getTeamById", "parameters": [{"name": "externalId", "required": true, "in": "path", "description": "Team external ID", "schema": {"example": 33, "type": "number"}}], "responses": {"200": {"description": "Team retrieved successfully", "content": {"application/json": {"example": {"data": {"id": 1, "externalId": 33, "name": "Manchester United", "code": "MUN", "country": "England", "founded": 1878, "national": false, "logo": "https://media.api-sports.io/football/teams/33.png"}, "status": 200}}}}}, "security": [{"bearer": []}], "summary": "Get Team by ID", "tags": ["Football - Teams"]}}, "/broadcast-links": {"post": {"description": "\n        Create a new broadcast link for a fixture.\n\n        **Access:** SystemUser only (Admin/Editor/Moderator)\n        **Features:**\n        - Automatic user tracking (addedBy)\n        - Fixture validation\n        - URL validation\n        - Audit logging\n\n        **Permissions:**\n        - Admin: Can create for any fixture\n        - Editor: Can create for any fixture\n        - Moderator: Can create for any fixture\n        ", "operationId": "BroadcastLinkController_createBroadcastLink", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBroadcastLinkDto"}}}}, "responses": {"201": {"description": "Broadcast link created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastLinkResponseDto"}}}}, "403": {"description": "SystemUser access required"}, "404": {"description": "Fixture not found"}}, "security": [{"bearer": []}], "summary": "Create Broadcast Link (SystemUser Only)", "tags": ["Broadcast Links"]}}, "/broadcast-links/fixture/{fixtureId}": {"get": {"description": "\n        Get broadcast links for a specific fixture with role-based filtering.\n\n        **Permissions:**\n        - Admin/Moderator: See all broadcast links for the fixture\n        - Editor: See only broadcast links they created\n\n        **Features:**\n        - Role-based data filtering\n        - Fixture validation\n        - Ownership-based access control\n        ", "operationId": "BroadcastLinkController_getBroadcastLinksByFixtureId", "parameters": [{"name": "fixtureId", "required": true, "in": "path", "description": "Fixture external ID", "schema": {"example": 868847, "type": "number"}}], "responses": {"200": {"description": "Broadcast links retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BroadcastLinkResponseDto"}}}}}, "404": {"description": "Fixture not found"}}, "security": [{"bearer": []}], "summary": "Get Broadcast Links by Fixture", "tags": ["Broadcast Links"]}}, "/broadcast-links/{id}": {"patch": {"description": "\n        Update a broadcast link with ownership-based permissions.\n\n        **Permissions:**\n        - Admin/Moderator: Can update any broadcast link\n        - Editor: Can only update broadcast links they created\n\n        **Features:**\n        - Ownership validation for editors\n        - URL validation\n        - Audit logging\n        ", "operationId": "BroadcastLinkController_updateBroadcastLink", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Broadcast link ID", "schema": {"example": 1, "type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBroadcastLinkDto"}}}}, "responses": {"200": {"description": "Broadcast link updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastLinkResponseDto"}}}}, "403": {"description": "Insufficient permissions"}, "404": {"description": "Broadcast link not found"}}, "security": [{"bearer": []}], "summary": "Update Broadcast Link", "tags": ["Broadcast Links"]}, "delete": {"description": "\n        Delete a broadcast link with ownership-based permissions.\n\n        **Permissions:**\n        - Admin/Moderator: Can delete any broadcast link\n        - Editor: Can only delete broadcast links they created\n\n        **Features:**\n        - Ownership validation for editors\n        - Soft delete with audit logging\n        - Permission enforcement\n        ", "operationId": "BroadcastLinkController_deleteBroadcastLink", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Broadcast link ID", "schema": {"example": 1, "type": "number"}}], "responses": {"204": {"description": "Broadcast link deleted successfully"}, "403": {"description": "Insufficient permissions"}, "404": {"description": "Broadcast link not found"}}, "security": [{"bearer": []}], "summary": "Delete Broadcast Link", "tags": ["Broadcast Links"]}}, "/system-auth/login": {"post": {"description": "\n        Login endpoint for system users (admin, editor).\n\n        **Features:**\n        - Username/password authentication\n        - JWT token generation\n        - Device tracking\n        - Audit logging\n        - Refresh token support\n\n        **Security:**\n        - Rate limiting applied\n        - Password validation\n        - Account status verification\n        - Login attempt logging\n        ", "operationId": "SystemAuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserLoginDto"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemAuthResponseDto"}}}}, "401": {"description": "Invalid credentials"}, "429": {"description": "Too many login attempts"}}, "summary": "System User Login", "tags": ["System Authentication"]}}, "/system-auth/create-user": {"post": {"description": "\n        Create a new system user (admin only).\n\n        **Features:**\n        - Admin-only access\n        - Role assignment\n        - Password hashing\n        - Email validation\n        - Username uniqueness check\n\n        **Roles:**\n        - admin: Full system access\n        - editor: Content management access\n        ", "operationId": "SystemAuthController_createUser", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserCreateDto"}}}}, "responses": {"201": {"description": "System user created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserProfileDto"}}}}, "403": {"description": "Admin access required"}, "409": {"description": "Username or email already exists"}}, "security": [{"bearer": []}], "summary": "Create System User", "tags": ["System Authentication"]}}, "/system-auth/refresh": {"post": {"description": "\n        Refresh an expired access token using a valid refresh token.\n\n        **Features:**\n        - Refresh token validation\n        - New access token generation\n        - Token expiry checking\n        - User status verification\n        ", "operationId": "SystemAuthController_refreshToken", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "Access token refreshed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "New JWT access token"}}}}}}, "401": {"description": "Invalid or expired refresh token"}}, "summary": "Refresh Access Token", "tags": ["System Authentication"]}}, "/system-auth/logout": {"post": {"description": "\n        Logout current session by revoking the refresh token.\n\n        **Features:**\n        - Refresh token revocation\n        - Session termination\n        - Audit logging\n        ", "operationId": "SystemAuthController_logout", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Logout successful"}}}}}}}, "summary": "Logout", "tags": ["System Authentication"]}}, "/system-auth/logout-all": {"post": {"description": "\n        Logout from all devices by revoking all refresh tokens for the current user.\n\n        **Features:**\n        - All refresh tokens revocation\n        - Multi-device session termination\n        - Security enhancement\n        ", "operationId": "SystemAuthController_logoutFromAllDevices", "parameters": [], "responses": {"200": {"description": "Logged out from all devices successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Logged out from all devices successfully"}}}}}}}, "security": [{"bearer": []}], "summary": "<PERSON><PERSON><PERSON> from All Devices", "tags": ["System Authentication"]}}, "/system-auth/profile": {"get": {"description": "\n        Get the profile information of the currently authenticated system user.\n\n        **Features:**\n        - Current user information\n        - Role and permissions\n        - Account status\n        - Last login information\n        ", "operationId": "SystemAuthController_getProfile", "parameters": [], "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserProfileDto"}}}}, "401": {"description": "Authentication required"}}, "security": [{"bearer": []}], "summary": "Get Current User Profile", "tags": ["System Authentication"]}, "put": {"description": "\n        Update system user profile information.\n\n        **Features:**\n        - Update email and full name (all users)\n        - Update role and active status (admin only)\n        - Email uniqueness validation\n        - Permission-based field updates\n\n        **Permissions:**\n        - All users: Can update email, fullName\n        - Admin only: Can update role, isActive\n\n        **Security:**\n        - Email uniqueness check\n        - Role-based access control\n        - Audit logging\n        ", "operationId": "SystemAuthController_updateProfile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserUpdateDto"}}}}, "responses": {"200": {"description": "User profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserProfileDto"}}}}, "403": {"description": "Forbidden - Insufficient permissions"}, "409": {"description": "Email already exists"}}, "security": [{"bearer": []}], "summary": "Update User Profile", "tags": ["System Authentication"]}}, "/system-auth/users/{id}": {"put": {"description": "\n        Update any system user profile by ID (admin only).\n\n        **Features:**\n        - Update any user's information\n        - Full admin control over all fields\n        - Email uniqueness validation\n        - Audit logging\n\n        **Admin Permissions:**\n        - Update email, fullName, role, isActive\n        - Manage any user account\n        - Activate/deactivate users\n        ", "operationId": "SystemAuthController_updateUser", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserUpdateDto"}}}}, "responses": {"200": {"description": "User updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserProfileDto"}}}}, "403": {"description": "Admin access required"}, "404": {"description": "User not found"}, "409": {"description": "Email already exists"}}, "security": [{"bearer": []}], "summary": "Update User by ID (Admin Only)", "tags": ["System Authentication"]}}, "/system-auth/change-password": {"post": {"description": "\n        Change current user's password.\n\n        **Features:**\n        - Current password verification\n        - New password confirmation\n        - Password strength validation\n        - Automatic logout from all devices\n\n        **Security:**\n        - Current password required\n        - Password confirmation match\n        - All sessions invalidated after change\n        - Audit logging\n        ", "operationId": "SystemAuthController_changePassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserChangePasswordDto"}}}}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password changed successfully. Please login again."}}}}}}, "400": {"description": "Invalid password or confirmation mismatch"}}, "security": [{"bearer": []}], "summary": "Change Password", "tags": ["System Authentication"]}}, "/users/register": {"post": {"description": "Register a new user account. Email verification required. Rate limited to 3 attempts per 5 minutes per IP.", "operationId": "RegisteredUserController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserRegisterDto"}}}}, "responses": {"201": {"description": "User registered successfully. Email verification required.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Registration successful. Please check your email for verification."}, "user": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "john_doe"}, "email": {"type": "string", "example": "<EMAIL>"}, "isEmailVerified": {"type": "boolean", "example": false}}}}}}}}, "400": {"description": "Invalid input data"}, "409": {"description": "Username or email already exists"}, "429": {"description": "Too many registration attempts"}}, "summary": "User Registration", "tags": ["Registered Users"]}}, "/users/login": {"post": {"description": "Authenticate user and receive JWT tokens. Email verification required. Rate limited to 5 attempts per minute per IP.", "operationId": "RegisteredUserController_login", "parameters": [{"name": "user-agent", "required": true, "in": "header", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserLoginDto"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserAuthResponseDto"}}}}, "401": {"description": "Invalid credentials or email not verified"}, "429": {"description": "Too many login attempts"}}, "summary": "User Login", "tags": ["Registered Users"]}}, "/users/verify-email": {"post": {"description": "Verify user email with verification token", "operationId": "RegisteredUserController_verifyEmail", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailVerificationDto"}}}}, "responses": {"200": {"description": "Email verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Email verified successfully"}, "user": {"$ref": "#/components/schemas/RegisteredUserProfileDto"}}}}}}, "400": {"description": "Invalid or expired verification token"}}, "summary": "<PERSON><PERSON><PERSON>", "tags": ["Registered Users"]}}, "/users/resend-verification": {"post": {"description": "Resend email verification token to user email", "operationId": "RegisteredUserController_resendVerification", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResendEmailVerificationDto"}}}}, "responses": {"200": {"description": "Verification email sent", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Verification email sent successfully"}}}}}}, "400": {"description": "Email already verified"}, "404": {"description": "User not found"}}, "summary": "Resend Email Verification", "tags": ["Registered Users"]}}, "/users/forgot-password": {"post": {"description": "Request password reset token via email", "operationId": "RegisteredUserController_forgotPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetRequestDto"}}}}, "responses": {"200": {"description": "Password reset email sent (if email exists)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "If the email exists, a password reset link has been sent"}}}}}}}, "summary": "Request Password Reset", "tags": ["Registered Users"]}}, "/users/reset-password": {"post": {"description": "Reset password using reset token", "operationId": "RegisteredUserController_resetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetDto"}}}}, "responses": {"200": {"description": "Password reset successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password reset successfully"}}}}}}, "400": {"description": "Invalid or expired reset token"}}, "summary": "Reset Password", "tags": ["Registered Users"]}}, "/users/profile": {"get": {"description": "Get current user profile information. Requires authentication.", "operationId": "RegisteredUserController_getProfile", "parameters": [], "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserProfileDto"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Get User Profile", "tags": ["Registered Users"]}, "put": {"description": "Update user profile information. Requires authentication.", "operationId": "RegisteredUserController_updateProfile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileDto"}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserProfileDto"}}}}, "400": {"description": "Invalid input data"}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Update User Profile", "tags": ["Registered Users"]}}, "/users/change-password": {"post": {"description": "Change user password. Requires current password verification.", "operationId": "RegisteredUserController_changePassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password changed successfully"}}}}}}, "400": {"description": "Invalid current password"}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Change Password", "tags": ["Registered Users"]}}, "/users/api-usage": {"get": {"description": "Get current month API usage statistics for the user", "operationId": "RegisteredUserController_getApiUsage", "parameters": [], "responses": {"200": {"description": "API usage statistics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"tier": {"type": "string", "example": "premium", "enum": ["free", "premium", "enterprise"]}, "apiCallsUsed": {"type": "number", "example": 150}, "apiCallsLimit": {"type": "number", "example": 10000, "nullable": true}, "apiCallsRemaining": {"type": "number", "example": 9850, "nullable": true}, "lastApiCallAt": {"type": "string", "format": "date-time", "nullable": true}, "resetDate": {"type": "string", "format": "date-time", "example": "2024-02-01T00:00:00.000Z"}}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Get API Usage Statistics", "tags": ["Registered Users"]}}, "/admin/tiers/statistics": {"get": {"description": "Get statistics about user distribution across tiers", "operationId": "AdminController_getTierStatistics", "parameters": [], "responses": {"200": {"description": "Tier statistics retrieved successfully", "content": {"application/json": {"example": {"free": 150, "premium": 45, "enterprise": 12, "total": 207}}}}}, "security": [{"bearer": []}], "summary": "Get Tier Statistics", "tags": ["Admin - User Management"]}}, "/admin/users/approaching-limits": {"get": {"description": "Get list of users who are approaching their API usage limits", "operationId": "AdminController_getUsersApproachingLimits", "parameters": [{"name": "threshold", "required": false, "in": "query", "description": "Usage percentage threshold (default: 80)", "schema": {"example": 80, "type": "number"}}], "responses": {"200": {"description": "Users approaching limits retrieved successfully"}}, "security": [{"bearer": []}], "summary": "Get Users Approaching API Limits", "tags": ["Admin - User Management"]}}, "/admin/users/{userId}/upgrade-tier": {"post": {"description": "Upgrade a user to a higher tier with optional subscription duration", "operationId": "AdminController_upgradeTier", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "User ID to upgrade", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"newTier": {"type": "string", "enum": ["premium", "enterprise"], "description": "New tier to upgrade to"}, "subscriptionMonths": {"type": "number", "description": "Subscription duration in months (for premium/enterprise)", "example": 12}}, "required": ["newTier"]}}}}, "responses": {"200": {"description": "User tier upgraded successfully"}, "400": {"description": "Invalid tier upgrade"}, "404": {"description": "User not found"}}, "security": [{"bearer": []}], "summary": "Upgrade User Tier", "tags": ["Admin - User Management"]}}, "/admin/users/{userId}/downgrade-tier": {"post": {"description": "Downgrade a user to a lower tier", "operationId": "AdminController_downgradeTier", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "User ID to downgrade", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"newTier": {"type": "string", "enum": ["free", "premium"], "description": "New tier to downgrade to"}}, "required": ["newTier"]}}}}, "responses": {"200": {"description": "User tier downgraded successfully"}}, "security": [{"bearer": []}], "summary": "Downgrade User Tier", "tags": ["Admin - User Management"]}}, "/admin/users/{userId}/extend-subscription": {"post": {"description": "Extend a user subscription by additional months", "operationId": "AdminController_extendSubscription", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "User ID to extend subscription", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"additionalMonths": {"type": "number", "description": "Additional months to extend", "example": 6}}, "required": ["additionalMonths"]}}}}, "responses": {"200": {"description": "Subscription extended successfully"}}, "security": [{"bearer": []}], "summary": "Extend User Subscription", "tags": ["Admin - User Management"]}}, "/admin/reset-api-usage": {"post": {"description": "Reset API usage counters for all users (typically run monthly)", "operationId": "AdminController_resetMonthlyApiUsage", "parameters": [], "responses": {"200": {"description": "API usage reset successfully"}}, "security": [{"bearer": []}], "summary": "Reset Monthly API Usage", "tags": ["Admin - User Management"]}}, "/admin/check-usage-warnings": {"post": {"description": "Check and send API usage warnings to users approaching limits", "operationId": "AdminController_checkApiUsageWarnings", "parameters": [], "responses": {"200": {"description": "API usage warnings checked and sent"}}, "security": [{"bearer": []}], "summary": "Check API Usage Warnings", "tags": ["Admin - User Management"]}}, "/admin/users/{userId}/subscription": {"get": {"description": "Get detailed subscription information for a user", "operationId": "AdminController_getSubscriptionInfo", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "User ID to get subscription info", "schema": {"type": "number"}}], "responses": {"200": {"description": "Subscription info retrieved successfully"}}, "security": [{"bearer": []}], "summary": "Get User Subscription Info", "tags": ["Admin - User Management"]}}, "/admin/users": {"get": {"description": "Get paginated list of all registered users with filtering", "operationId": "AdminController_getAllUsers", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"example": 20, "type": "number"}}, {"name": "tier", "required": false, "in": "query", "description": "Filter by tier", "schema": {"enum": ["free", "premium", "enterprise"], "type": "string"}}, {"name": "isActive", "required": false, "in": "query", "description": "Filter by active status", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Users retrieved successfully"}}, "security": [{"bearer": []}], "summary": "Get All Registered Users", "tags": ["Admin - User Management"]}}}, "info": {"title": "APISportsGame API", "description": "Enterprise Sports API với complete authentication system và optimized sync performance. Features: Enhanced JWT authentication, rate limiting, audit logging, smart live sync với 96% API call reduction.", "version": "2.0.0", "contact": {"name": "APISportsGame Team", "url": "https://apisportsgame.com", "email": "<EMAIL>"}}, "tags": [{"name": "Authentication", "description": "Authentication endpoints - <PERSON><PERSON>, logout, user management"}, {"name": "Football", "description": "Football data endpoints - Fixtures, teams, leagues"}, {"name": "Admin Management", "description": "Admin management - User administration, system controls"}, {"name": "Public", "description": "Public endpoints - No authentication required"}, {"name": "Data Synchronization", "description": "Data synchronization - Manual sync triggers, status"}], "servers": [{"url": "http://*************", "description": "Production Server"}, {"url": "http://localhost:3000", "description": "Local Development"}], "components": {"securitySchemes": {"JWT": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"CreateFixtureDto": {"type": "object", "properties": {}}, "UpdateFixtureDto": {"type": "object", "properties": {}}, "CreateLeagueDto": {"type": "object", "properties": {}}, "UpdateLeagueDto": {"type": "object", "properties": {}}, "CreateBroadcastLinkDto": {"type": "object", "properties": {"fixtureId": {"type": "number", "description": "Fixture external ID", "example": 868847}, "linkName": {"type": "string", "description": "Name of the broadcast link", "example": "YouTube Live Stream"}, "linkUrl": {"type": "string", "description": "URL of the broadcast link", "example": "https://youtube.com/watch?v=abc123"}, "linkComment": {"type": "string", "description": "Comment or description for the link", "example": "Official HD stream with English commentary"}}, "required": ["fixtureId", "linkName", "linkUrl", "linkComment"]}, "BroadcastLinkResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "Broadcast link ID", "example": 1}, "fixtureId": {"type": "number", "description": "Fixture external ID", "example": 868847}, "linkName": {"type": "string", "description": "Name of the broadcast link", "example": "YouTube Live Stream"}, "linkComment": {"type": "string", "description": "Comment or description for the link", "example": "Official HD stream with English commentary"}, "linkUrl": {"type": "string", "description": "URL of the broadcast link", "example": "https://youtube.com/watch?v=abc123"}, "addedBy": {"type": "number", "description": "ID of user who added this link", "example": 1}, "createdAt": {"type": "string", "description": "Creation timestamp", "example": "2024-05-24T10:30:00.000Z"}, "updatedAt": {"type": "string", "description": "Last update timestamp", "example": "2024-05-24T10:30:00.000Z"}}, "required": ["id", "fixtureId", "linkName", "linkComment", "linkUrl", "added<PERSON>y", "createdAt", "updatedAt"]}, "UpdateBroadcastLinkDto": {"type": "object", "properties": {"linkName": {"type": "string", "description": "Name of the broadcast link", "example": "Updated YouTube Live Stream"}, "linkUrl": {"type": "string", "description": "URL of the broadcast link", "example": "https://youtube.com/watch?v=updated123"}, "linkComment": {"type": "string", "description": "Comment or description for the link", "example": "Updated HD stream with multiple languages"}}}, "SystemUserLoginDto": {"type": "object", "properties": {"username": {"type": "string", "description": "Username for system user", "example": "admin"}, "password": {"type": "string", "description": "Password for system user", "example": "admin123456"}}, "required": ["username", "password"]}, "SystemAuthResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "JWT access token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "refreshToken": {"type": "string", "description": "JWT refresh token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "user": {"type": "object", "description": "System user information"}}, "required": ["accessToken", "refreshToken", "user"]}, "SystemUserCreateDto": {"type": "object", "properties": {"username": {"type": "string", "description": "Username for new system user", "example": "editor1"}, "email": {"type": "string", "description": "Email for new system user", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password for new system user", "example": "SecurePassword123!"}, "role": {"type": "string", "description": "Role for new system user", "enum": ["admin", "editor", "moderator"], "example": "editor"}, "fullName": {"type": "string", "description": "Full name of the system user", "example": "John <PERSON>"}}, "required": ["username", "email", "password", "role"]}, "SystemUserProfileDto": {"type": "object", "properties": {"id": {"type": "number", "description": "User ID", "example": 1}, "username": {"type": "string", "description": "Username", "example": "admin"}, "email": {"type": "string", "description": "Email address", "example": "<EMAIL>"}, "fullName": {"type": "string", "description": "Full name", "example": "System Administrator"}, "role": {"type": "string", "description": "User role", "enum": ["admin", "editor", "moderator"], "example": "admin"}, "isActive": {"type": "boolean", "description": "Account status", "example": true}, "lastLoginAt": {"format": "date-time", "type": "string", "description": "Last login timestamp", "example": "2024-01-15T10:30:00Z"}, "createdAt": {"format": "date-time", "type": "string", "description": "Account creation timestamp", "example": "2024-01-01T00:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update timestamp", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "username", "email", "role", "isActive", "createdAt", "updatedAt"]}, "RefreshTokenDto": {"type": "object", "properties": {"refreshToken": {"type": "string", "description": "Refresh token string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}, "required": ["refreshToken"]}, "SystemUserUpdateDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email for system user", "example": "<EMAIL>"}, "fullName": {"type": "string", "description": "Full name of the system user", "example": "<PERSON> Updated Editor"}, "role": {"type": "string", "description": "Role for system user (admin only)", "enum": ["admin", "editor", "moderator"], "example": "editor"}, "isActive": {"type": "boolean", "description": "Active status (admin only)", "example": true}}}, "SystemUserChangePasswordDto": {"type": "object", "properties": {"currentPassword": {"type": "string", "description": "Current password", "example": "CurrentPassword123!"}, "newPassword": {"type": "string", "description": "New password", "example": "NewSecurePassword123!"}, "confirmPassword": {"type": "string", "description": "Confirm new password", "example": "NewSecurePassword123!"}}, "required": ["currentPassword", "newPassword", "confirmPassword"]}, "RegisteredUserRegisterDto": {"type": "object", "properties": {"username": {"type": "string", "description": "Username for the account", "example": "john_doe", "minLength": 3, "maxLength": 50}, "email": {"type": "string", "description": "Email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password for the account", "example": "SecurePassword123!", "minLength": 8, "maxLength": 128}, "fullName": {"type": "string", "description": "Full name of the user", "example": "<PERSON>", "maxLength": 100}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>", "maxLength": 50}, "lastName": {"type": "string", "description": "Last name", "example": "<PERSON><PERSON>", "maxLength": 50}, "displayName": {"type": "string", "description": "Display name", "example": "John<PERSON>", "maxLength": 50}}, "required": ["username", "email", "password"]}, "RegisteredUserLoginDto": {"type": "object", "properties": {"usernameOrEmail": {"type": "string", "description": "Username or email", "example": "john_doe"}, "password": {"type": "string", "description": "Password", "example": "SecurePassword123!"}}, "required": ["usernameOrEmail", "password"]}, "RegisteredUserProfileDto": {"type": "object", "properties": {"id": {"type": "number", "description": "User ID", "example": 1}, "username": {"type": "string", "description": "Username", "example": "john_doe"}, "email": {"type": "string", "description": "Email address", "example": "<EMAIL>"}, "fullName": {"type": "string", "description": "Full name", "example": "<PERSON>"}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name", "example": "<PERSON><PERSON>"}, "displayName": {"type": "string", "description": "Display name", "example": "John<PERSON>"}, "tier": {"type": "string", "description": "User tier", "example": "free", "enum": ["free", "premium", "enterprise"]}, "isActive": {"type": "boolean", "description": "Account active status", "example": true}, "isEmailVerified": {"type": "boolean", "description": "Email verification status", "example": true}, "apiCallsUsed": {"type": "number", "description": "API calls used this month", "example": 150}, "apiCallsLimit": {"type": "object", "description": "API calls limit per month", "example": 1000, "nullable": true}, "apiCallsRemaining": {"type": "object", "description": "API calls remaining this month", "example": 850, "nullable": true}, "hasActiveSubscription": {"type": "boolean", "description": "Subscription active status", "example": true}, "subscriptionEndDate": {"format": "date-time", "type": "string", "description": "Subscription end date", "example": "2024-12-31T23:59:59.000Z"}, "lastLoginAt": {"format": "date-time", "type": "string", "description": "Last login timestamp"}, "createdAt": {"format": "date-time", "type": "string", "description": "Account creation timestamp"}}, "required": ["id", "username", "email", "fullName", "tier", "isActive", "isEmailVerified", "apiCallsUsed", "apiCallsLimit", "apiCallsRemaining", "hasActiveSubscription", "createdAt"]}, "RegisteredUserAuthResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "Access token"}, "refreshToken": {"type": "string", "description": "Refresh token"}, "user": {"description": "User profile information", "allOf": [{"$ref": "#/components/schemas/RegisteredUserProfileDto"}]}}, "required": ["accessToken", "refreshToken", "user"]}, "EmailVerificationDto": {"type": "object", "properties": {"token": {"type": "string", "description": "Email verification token", "example": "abc123def456"}}, "required": ["token"]}, "ResendEmailVerificationDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email address to resend verification", "example": "<EMAIL>"}}, "required": ["email"]}, "PasswordResetRequestDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email address for password reset", "example": "<EMAIL>"}}, "required": ["email"]}, "PasswordResetDto": {"type": "object", "properties": {"token": {"type": "string", "description": "Password reset token", "example": "reset123token456"}, "newPassword": {"type": "string", "description": "New password", "example": "NewSecurePassword123!", "minLength": 8, "maxLength": 128}}, "required": ["token", "newPassword"]}, "UpdateProfileDto": {"type": "object", "properties": {"fullName": {"type": "string", "description": "Full name", "example": "<PERSON>"}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name", "example": "<PERSON><PERSON>"}, "displayName": {"type": "string", "description": "Display name", "example": "JohnD_Updated"}}}, "ChangePasswordDto": {"type": "object", "properties": {"currentPassword": {"type": "string", "description": "Current password", "example": "CurrentPassword123!"}, "newPassword": {"type": "string", "description": "New password", "example": "NewPassword123!", "minLength": 8, "maxLength": 128}}, "required": ["currentPassword", "newPassword"]}}}}